package com.ruoyi.vo.basicData;
import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.Data;
import org.springframework.format.annotation.DateTimeFormat;

import java.util.Date;

/**
 * mes物料批次明细
 */
@Data
public class DocumentInventoryDetailDto {

    private String id;

    private String detailCode;

    private String inventoryCode;

    private String location;

    private String materialCode;

    private Integer quantity;

    private String remark;

    private Integer completedNum;

    private Integer batchStatus;

    private String containerCode;

    /**
     * 创建时间
     */
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @JsonFormat(shape = JsonFormat.Shape.STRING, pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date createTime;
}